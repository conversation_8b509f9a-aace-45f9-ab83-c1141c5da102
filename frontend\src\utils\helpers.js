// Date and time utilities
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const formatDateTime = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const calculateDaysSince = (dateString) => {
  const today = new Date();
  const targetDate = new Date(dateString);
  const diffTime = Math.abs(today - targetDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const formatDonationTime = (days) => {
  if (days === 0) return 'Donated today';
  if (days === 1) return 'Donated yesterday';
  if (days < 30) return `Donated ${days} days ago`;
  if (days < 365) {
    const months = Math.floor(days / 30);
    return `Donated ${months} month${months > 1 ? 's' : ''} ago`;
  }
  const years = Math.floor(days / 365);
  return `Donated ${years} year${years > 1 ? 's' : ''} ago`;
};

// Phone number utilities
export const formatPhoneNumber = (phone) => {
  if (!phone) return '';
  // Format Bangladeshi phone numbers
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11 && cleaned.startsWith('01')) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

export const validatePhoneNumber = (phone) => {
  const bangladeshiMobileRegex = /^01[3-9]\d{8}$/;
  return bangladeshiMobileRegex.test(phone);
};

export const maskPhoneNumber = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{3,})/, '$1****$2');
};

// Text utilities
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const capitalizeFirstLetter = (string) => {
  if (!string) return '';
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
};

export const formatName = (firstName, lastName) => {
  if (!firstName && !lastName) return 'Unknown';
  if (!lastName) return firstName;
  return `${firstName} ${lastName.charAt(0)}.`;
};

// Validation utilities
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateRequired = (value) => {
  return value && value.toString().trim().length > 0;
};

// Blood group utilities
export const getBloodGroupCompatibility = (bloodGroup) => {
  const compatibility = {
    'A+': ['A+', 'AB+'],
    'A-': ['A+', 'A-', 'AB+', 'AB-'],
    'B+': ['B+', 'AB+'],
    'B-': ['B+', 'B-', 'AB+', 'AB-'],
    'AB+': ['AB+'],
    'AB-': ['AB+', 'AB-'],
    'O+': ['A+', 'B+', 'AB+', 'O+'],
    'O-': ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
  };
  return compatibility[bloodGroup] || [];
};

export const canDonateTo = (donorBloodGroup, recipientBloodGroup) => {
  const compatibility = getBloodGroupCompatibility(donorBloodGroup);
  return compatibility.includes(recipientBloodGroup);
};

// Error handling utilities
export const getErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.response?.data?.error) return error.response.data.error;
  if (error?.response?.data?.message) return error.response.data.message;
  return 'An unexpected error occurred';
};

// Storage utilities
export const formatStorageKey = (key) => {
  return `@LifeDonor:${key}`;
};

// Debounce utility
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
