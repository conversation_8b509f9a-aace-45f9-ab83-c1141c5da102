import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider } from 'react-native-paper';
import { StatusBar } from 'expo-status-bar';
import { theme } from './src/constants/theme';

// Import screens (we'll create these next)
import HomeScreen from './src/screens/HomeScreen';
import DonorRegistrationScreen from './src/screens/DonorRegistrationScreen';
import DonorDetailsScreen from './src/screens/DonorDetailsScreen';
import AdminPanelScreen from './src/screens/AdminPanelScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <PaperProvider theme={theme}>
      <NavigationContainer>
        <StatusBar style="auto" />
        <Stack.Navigator
          initialRouteName="Home"
          screenOptions={{
            headerStyle: {
              backgroundColor: '#e74c3c',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}
        >
          <Stack.Screen 
            name="Home" 
            component={HomeScreen} 
            options={{ title: 'LifeDonor - Find Blood Donors' }}
          />
          <Stack.Screen 
            name="DonorRegistration" 
            component={DonorRegistrationScreen} 
            options={{ title: 'Register as Donor' }}
          />
          <Stack.Screen 
            name="DonorDetails" 
            component={DonorDetailsScreen} 
            options={{ title: 'Donor Details' }}
          />
          <Stack.Screen 
            name="AdminPanel" 
            component={AdminPanelScreen} 
            options={{ title: 'Admin Panel' }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </PaperProvider>
  );
}
