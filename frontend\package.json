{"name": "lifedonor-frontend", "version": "1.0.0", "description": "React Native Expo frontend for LifeDonor blood donor management app", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "start:clear": "expo start --clear", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "expo build:android", "build:ios": "expo build:ios", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "keywords": ["react-native", "expo", "blood-donor", "healthcare", "mobile-app", "bangladesh"], "author": "LifeDonor Team", "license": "MIT", "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "axios": "^1.11.0", "expo": "~52.0.0", "expo-status-bar": "~2.0.0", "react": "^18.3.1", "react-hook-form": "^7.60.0", "react-native": "^0.76.5", "react-native-elements": "^3.4.3", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2"}, "private": true}