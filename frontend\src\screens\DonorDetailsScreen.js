import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  Linking,
  Platform
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
  Text,
  Divider,
  IconButton,
  ActivityIndicator
} from 'react-native-paper';
import { donorService } from '../services';
import { getBloodGroupColor } from '../constants/bloodGroups';

const DonorDetailsScreen = ({ route, navigation }) => {
  const { donor: initialDonor } = route.params;
  const [donor, setDonor] = useState(initialDonor);
  const [loading, setLoading] = useState(false);
  const [showFullPhone, setShowFullPhone] = useState(false);

  useEffect(() => {
    // Load full donor details if we only have basic info
    if (initialDonor && !donor.phoneNumber.includes('*')) {
      loadDonorDetails();
    }
  }, []);

  const loadDonorDetails = async () => {
    setLoading(true);
    try {
      const fullDonor = await donorService.getDonorById(initialDonor.id);
      setDonor(fullDonor);
    } catch (error) {
      Alert.alert('Error', 'Failed to load donor details: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const calculateDaysSinceLastDonation = (lastDonationDate) => {
    const today = new Date();
    const donationDate = new Date(lastDonationDate);
    const diffTime = Math.abs(today - donationDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDonationText = (days) => {
    if (days === 0) return 'Donated today';
    if (days === 1) return 'Donated yesterday';
    if (days < 30) return `Donated ${days} days ago`;
    if (days < 365) {
      const months = Math.floor(days / 30);
      return `Donated ${months} month${months > 1 ? 's' : ''} ago`;
    }
    const years = Math.floor(days / 365);
    return `Donated ${years} year${years > 1 ? 's' : ''} ago`;
  };

  const handleCall = () => {
    if (!showFullPhone) {
      setShowFullPhone(true);
      return;
    }

    const phoneNumber = donor.phoneNumber.replace(/\*/g, '');
    const phoneUrl = Platform.OS === 'ios' ? `telprompt:${phoneNumber}` : `tel:${phoneNumber}`;

    Linking.canOpenURL(phoneUrl)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(phoneUrl);
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device');
        }
      })
      .catch((err) => {
        Alert.alert('Error', 'Failed to make call: ' + err.message);
      });
  };

  const handleSMS = () => {
    if (!showFullPhone) {
      setShowFullPhone(true);
      return;
    }

    const phoneNumber = donor.phoneNumber.replace(/\*/g, '');
    const smsUrl = Platform.OS === 'ios' ? `sms:${phoneNumber}` : `sms:${phoneNumber}`;

    Linking.canOpenURL(smsUrl)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(smsUrl);
        } else {
          Alert.alert('Error', 'SMS is not supported on this device');
        }
      })
      .catch((err) => {
        Alert.alert('Error', 'Failed to send SMS: ' + err.message);
      });
  };

  const handleReport = () => {
    Alert.alert(
      'Report Donor',
      'Are you sure you want to report this donor listing?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Report',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Reported', 'Thank you for your report. We will review this listing.');
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#e74c3c" />
        <Text style={styles.loadingText}>Loading donor details...</Text>
      </View>
    );
  }

  const daysSinceLastDonation = calculateDaysSinceLastDonation(donor.lastDonationDate);
  const bloodGroupColor = getBloodGroupColor(donor.bloodGroup);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Card */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <View style={styles.donorInfo}>
              <Title style={styles.donorName}>
                {donor.firstName} {donor.lastName}
              </Title>
              <Paragraph style={styles.donorLocation}>
                {donor.currentLocation}
              </Paragraph>
            </View>
            <Chip
              style={[styles.bloodGroupChip, { backgroundColor: bloodGroupColor }]}
              textStyle={styles.bloodGroupText}
            >
              {donor.bloodGroup}
            </Chip>
          </View>

          <View style={styles.statusContainer}>
            <Chip
              style={[
                styles.availabilityChip,
                { backgroundColor: donor.isAvailable ? '#27ae60' : '#e74c3c' }
              ]}
              textStyle={styles.availabilityText}
              icon={donor.isAvailable ? 'check-circle' : 'close-circle'}
            >
              {donor.isAvailable ? 'Available' : 'Unavailable'}
            </Chip>

            <Text style={styles.donationText}>
              {formatDonationText(daysSinceLastDonation)}
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Contact Information Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Contact Information</Title>
          <Divider style={styles.divider} />

          <View style={styles.contactRow}>
            <Text style={styles.contactLabel}>Phone Number:</Text>
            <Text style={styles.contactValue}>
              {showFullPhone ? donor.phoneNumber.replace(/\*/g, '') : donor.phoneNumber}
            </Text>
          </View>

          <View style={styles.contactActions}>
            <Button
              mode="contained"
              onPress={handleCall}
              style={[styles.contactButton, styles.callButton]}
              icon="phone"
            >
              {showFullPhone ? 'Call' : 'Show Contact'}
            </Button>

            {showFullPhone && (
              <Button
                mode="outlined"
                onPress={handleSMS}
                style={[styles.contactButton, styles.smsButton]}
                icon="message"
              >
                SMS
              </Button>
            )}
          </View>
        </Card.Content>
      </Card>

      {/* Location Details Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Location Details</Title>
          <Divider style={styles.divider} />

          <View style={styles.locationRow}>
            <Text style={styles.locationLabel}>Current Location:</Text>
            <Text style={styles.locationValue}>{donor.currentLocation}</Text>
          </View>

          {donor.village && (
            <View style={styles.locationRow}>
              <Text style={styles.locationLabel}>Village:</Text>
              <Text style={styles.locationValue}>{donor.village}</Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Additional Information Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Additional Information</Title>
          <Divider style={styles.divider} />

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Last Donation:</Text>
            <Text style={styles.infoValue}>
              {new Date(donor.lastDonationDate).toLocaleDateString()}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Member Since:</Text>
            <Text style={styles.infoValue}>
              {new Date(donor.createdAt).toLocaleDateString()}
            </Text>
          </View>

          {donor.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Notes:</Text>
              <Text style={styles.notesValue}>{donor.notes}</Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <Button
          mode="outlined"
          onPress={handleReport}
          style={styles.reportButton}
          icon="flag"
          textColor="#e74c3c"
        >
          Report Listing
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 4,
  },
  card: {
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  donorInfo: {
    flex: 1,
  },
  donorName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  donorLocation: {
    fontSize: 16,
    color: '#666',
  },
  bloodGroupChip: {
    elevation: 2,
    marginLeft: 16,
  },
  bloodGroupText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availabilityChip: {
    elevation: 1,
  },
  availabilityText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  donationText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 8,
  },
  divider: {
    marginBottom: 16,
  },
  contactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  contactValue: {
    fontSize: 16,
    color: '#666',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  contactActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  contactButton: {
    flex: 1,
  },
  callButton: {
    backgroundColor: '#27ae60',
  },
  smsButton: {
    borderColor: '#3498db',
  },
  locationRow: {
    marginBottom: 12,
  },
  locationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  locationValue: {
    fontSize: 16,
    color: '#666',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  infoValue: {
    fontSize: 14,
    color: '#666',
  },
  notesContainer: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  notesValue: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionContainer: {
    margin: 16,
    marginTop: 8,
  },
  reportButton: {
    borderColor: '#e74c3c',
  },
});

export default DonorDetailsScreen;
