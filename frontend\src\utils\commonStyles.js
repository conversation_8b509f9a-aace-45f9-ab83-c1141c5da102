import { StyleSheet } from 'react-native';
import { colors, spacing, typography, shadows } from '../constants/theme';

export const commonStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: colors.light,
  },
  safeContainer: {
    flex: 1,
    backgroundColor: colors.light,
    paddingTop: spacing.md,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: spacing.xl,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.light,
  },

  // Card styles
  card: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: spacing.md,
    margin: spacing.md,
    ...shadows.medium,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  cardTitle: {
    ...typography.h5,
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  cardContent: {
    paddingVertical: spacing.sm,
  },

  // Text styles
  title: {
    ...typography.h3,
    color: colors.dark,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body1,
    color: colors.muted,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...typography.h6,
    color: colors.dark,
    marginBottom: spacing.sm,
  },
  label: {
    ...typography.body2,
    color: colors.dark,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  requiredLabel: {
    ...typography.body2,
    color: colors.dark,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  errorText: {
    ...typography.caption,
    color: colors.danger,
    marginTop: spacing.xs,
  },
  helperText: {
    ...typography.caption,
    color: colors.muted,
    marginTop: spacing.xs,
  },

  // Button styles
  primaryButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 8,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  buttonText: {
    ...typography.button,
    color: colors.white,
    textAlign: 'center',
  },
  secondaryButtonText: {
    ...typography.button,
    color: colors.primary,
    textAlign: 'center',
  },

  // Input styles
  input: {
    borderWidth: 1,
    borderColor: colors.muted,
    borderRadius: 8,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    ...typography.body1,
    marginBottom: spacing.sm,
  },
  inputFocused: {
    borderColor: colors.primary,
    borderWidth: 2,
  },
  inputError: {
    borderColor: colors.danger,
  },

  // Layout styles
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rowCenter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
  },
  flex1: {
    flex: 1,
  },

  // Spacing styles
  marginXS: { margin: spacing.xs },
  marginSM: { margin: spacing.sm },
  marginMD: { margin: spacing.md },
  marginLG: { margin: spacing.lg },
  marginXL: { margin: spacing.xl },

  paddingXS: { padding: spacing.xs },
  paddingSM: { padding: spacing.sm },
  paddingMD: { padding: spacing.md },
  paddingLG: { padding: spacing.lg },
  paddingXL: { padding: spacing.xl },

  marginTopXS: { marginTop: spacing.xs },
  marginTopSM: { marginTop: spacing.sm },
  marginTopMD: { marginTop: spacing.md },
  marginTopLG: { marginTop: spacing.lg },
  marginTopXL: { marginTop: spacing.xl },

  marginBottomXS: { marginBottom: spacing.xs },
  marginBottomSM: { marginBottom: spacing.sm },
  marginBottomMD: { marginBottom: spacing.md },
  marginBottomLG: { marginBottom: spacing.lg },
  marginBottomXL: { marginBottom: spacing.xl },

  // Blood group styles
  bloodGroupChip: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  bloodGroupText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: 'bold',
  },

  // Status styles
  statusChip: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.light,
  },
  loadingText: {
    ...typography.body1,
    color: colors.muted,
    marginTop: spacing.md,
  },

  // Empty state styles
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyStateText: {
    ...typography.h6,
    color: colors.muted,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  emptyStateSubtext: {
    ...typography.body2,
    color: colors.muted,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },

  // Shadow styles
  shadowSmall: shadows.small,
  shadowMedium: shadows.medium,
  shadowLarge: shadows.large,

  // Divider
  divider: {
    height: 1,
    backgroundColor: colors.muted,
    opacity: 0.3,
    marginVertical: spacing.sm,
  },

  // FAB styles
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary,
  },
});
