/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 */

'use strict';

function _slicedToArray(r, e) {
  return (
    _arrayWithHoles(r) ||
    _iterableToArrayLimit(r, e) ||
    _unsupportedIterableToArray(r, e) ||
    _nonIterableRest()
  );
}
function _nonIterableRest() {
  throw new TypeError(
    'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
  );
}
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if ('string' == typeof r) return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return (
      'Object' === t && r.constructor && (t = r.constructor.name),
      'Map' === t || 'Set' === t
        ? Array.from(r)
        : 'Arguments' === t ||
          /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)
        ? _arrayLikeToArray(r, a)
        : void 0
    );
  }
}
function _arrayLikeToArray(r, a) {
  (null == a || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
function _iterableToArrayLimit(r, l) {
  var t =
    null == r
      ? null
      : ('undefined' != typeof Symbol && r[Symbol.iterator]) || r['@@iterator'];
  if (null != t) {
    var e,
      n,
      i,
      u,
      a = [],
      f = !0,
      o = !1;
    try {
      if (((i = (t = t.call(r)).next), 0 === l)) {
        if (Object(t) !== t) return;
        f = !1;
      } else
        for (
          ;
          !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l);
          f = !0
        );
    } catch (r) {
      (o = !0), (n = r);
    } finally {
      try {
        if (!f && null != t.return && ((u = t.return()), Object(u) !== u))
          return;
      } finally {
        if (o) throw n;
      }
    }
    return a;
  }
}
function _arrayWithHoles(r) {
  if (Array.isArray(r)) return r;
}
const _require = require('../../parsers/parsers-commons'),
  unwrapNullable = _require.unwrapNullable;
const _require2 = require('./Utils'),
  createAliasResolver = _require2.createAliasResolver,
  getModules = _require2.getModules;
const HostFunctionTemplate = ({
  hasteModuleName,
  methodName,
  returnTypeAnnotation,
  args,
}) => {
  const isNullable = returnTypeAnnotation.type === 'NullableTypeAnnotation';
  const isVoid = returnTypeAnnotation.type === 'VoidTypeAnnotation';
  const methodCallArgs = ['    rt', ...args].join(',\n    ');
  const methodCall = `static_cast<${hasteModuleName}CxxSpecJSI *>(&turboModule)->${methodName}(\n${methodCallArgs}\n  )`;
  return `static jsi::Value __hostFunction_${hasteModuleName}CxxSpecJSI_${methodName}(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {${
    isVoid
      ? `\n  ${methodCall};`
      : isNullable
      ? `\n  auto result = ${methodCall};`
      : ''
  }
  return ${
    isVoid
      ? 'jsi::Value::undefined()'
      : isNullable
      ? 'result ? jsi::Value(std::move(*result)) : jsi::Value::null()'
      : methodCall
  };
}`;
};
const ModuleTemplate = ({
  hasteModuleName,
  hostFunctions,
  moduleName,
  methods,
}) => {
  return `${hostFunctions.join('\n')}

${hasteModuleName}CxxSpecJSI::${hasteModuleName}CxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("${moduleName}", jsInvoker) {
${methods
  .map(({methodName, paramCount}) => {
    return `  methodMap_["${methodName}"] = MethodMetadata {${paramCount}, __hostFunction_${hasteModuleName}CxxSpecJSI_${methodName}};`;
  })
  .join('\n')}
}`;
};
const FileTemplate = ({libraryName, modules}) => {
  return `/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * ${'@'}generated by codegen project: GenerateModuleCpp.js
 */

#include "${libraryName}JSI.h"

namespace facebook::react {

${modules}


} // namespace facebook::react
`;
};
function serializeArg(moduleName, arg, index, resolveAlias, enumMap) {
  const nullableTypeAnnotation = arg.typeAnnotation,
    optional = arg.optional;
  const _unwrapNullable = unwrapNullable(nullableTypeAnnotation),
    _unwrapNullable2 = _slicedToArray(_unwrapNullable, 2),
    typeAnnotation = _unwrapNullable2[0],
    nullable = _unwrapNullable2[1];
  let realTypeAnnotation = typeAnnotation;
  if (realTypeAnnotation.type === 'TypeAliasTypeAnnotation') {
    realTypeAnnotation = resolveAlias(realTypeAnnotation.name);
  }
  function wrap(callback) {
    const val = `args[${index}]`;
    const expression = callback(val);

    // param?: T
    if (optional && !nullable) {
      // throw new Error('are we hitting this case? ' + moduleName);
      return `count <= ${index} || ${val}.isUndefined() ? std::nullopt : std::make_optional(${expression})`;
    }

    // param: ?T
    // param?: ?T
    if (nullable || optional) {
      return `count <= ${index} || ${val}.isNull() || ${val}.isUndefined() ? std::nullopt : std::make_optional(${expression})`;
    }

    // param: T
    return `count <= ${index} ? throw jsi::JSError(rt, "Expected argument in position ${index} to be passed") : ${expression}`;
  }
  switch (realTypeAnnotation.type) {
    case 'ReservedTypeAnnotation':
      switch (realTypeAnnotation.name) {
        case 'RootTag':
          return wrap(val => `${val}.asNumber()`);
        default:
          realTypeAnnotation.name;
          throw new Error(
            `Unknown prop type for "${arg.name}, found: ${realTypeAnnotation.name}"`,
          );
      }
    case 'StringTypeAnnotation':
      return wrap(val => `${val}.asString(rt)`);
    case 'BooleanTypeAnnotation':
      return wrap(val => `${val}.asBool()`);
    case 'EnumDeclaration':
      switch (realTypeAnnotation.memberType) {
        case 'NumberTypeAnnotation':
          return wrap(val => `${val}.asNumber()`);
        case 'StringTypeAnnotation':
          return wrap(val => `${val}.asString(rt)`);
        default:
          throw new Error(
            `Unknown enum type for "${arg.name}, found: ${realTypeAnnotation.type}"`,
          );
      }
    case 'NumberTypeAnnotation':
      return wrap(val => `${val}.asNumber()`);
    case 'FloatTypeAnnotation':
      return wrap(val => `${val}.asNumber()`);
    case 'DoubleTypeAnnotation':
      return wrap(val => `${val}.asNumber()`);
    case 'Int32TypeAnnotation':
      return wrap(val => `${val}.asNumber()`);
    case 'ArrayTypeAnnotation':
      return wrap(val => `${val}.asObject(rt).asArray(rt)`);
    case 'FunctionTypeAnnotation':
      return wrap(val => `${val}.asObject(rt).asFunction(rt)`);
    case 'GenericObjectTypeAnnotation':
      return wrap(val => `${val}.asObject(rt)`);
    case 'UnionTypeAnnotation':
      switch (typeAnnotation.memberType) {
        case 'NumberTypeAnnotation':
          return wrap(val => `${val}.asNumber()`);
        case 'ObjectTypeAnnotation':
          return wrap(val => `${val}.asObject(rt)`);
        case 'StringTypeAnnotation':
          return wrap(val => `${val}.asString(rt)`);
        default:
          throw new Error(
            `Unsupported union member type for param  "${arg.name}, found: ${realTypeAnnotation.memberType}"`,
          );
      }
    case 'ObjectTypeAnnotation':
      return wrap(val => `${val}.asObject(rt)`);
    case 'MixedTypeAnnotation':
      return wrap(val => `jsi::Value(rt, ${val})`);
    default:
      realTypeAnnotation.type;
      throw new Error(
        `Unknown prop type for "${arg.name}, found: ${realTypeAnnotation.type}"`,
      );
  }
}
function serializePropertyIntoHostFunction(
  moduleName,
  hasteModuleName,
  property,
  resolveAlias,
  enumMap,
) {
  const _unwrapNullable3 = unwrapNullable(property.typeAnnotation),
    _unwrapNullable4 = _slicedToArray(_unwrapNullable3, 1),
    propertyTypeAnnotation = _unwrapNullable4[0];
  return HostFunctionTemplate({
    hasteModuleName,
    methodName: property.name,
    returnTypeAnnotation: propertyTypeAnnotation.returnTypeAnnotation,
    args: propertyTypeAnnotation.params.map((p, i) =>
      serializeArg(moduleName, p, i, resolveAlias, enumMap),
    ),
  });
}
module.exports = {
  generate(
    libraryName,
    schema,
    packageName,
    assumeNonnull = false,
    headerPrefix,
  ) {
    const nativeModules = getModules(schema);
    const modules = Object.keys(nativeModules)
      .map(hasteModuleName => {
        const nativeModule = nativeModules[hasteModuleName];
        const aliasMap = nativeModule.aliasMap,
          enumMap = nativeModule.enumMap,
          methods = nativeModule.spec.methods,
          moduleName = nativeModule.moduleName;
        const resolveAlias = createAliasResolver(aliasMap);
        const hostFunctions = methods.map(property =>
          serializePropertyIntoHostFunction(
            moduleName,
            hasteModuleName,
            property,
            resolveAlias,
            enumMap,
          ),
        );
        return ModuleTemplate({
          hasteModuleName,
          hostFunctions,
          moduleName,
          methods: methods.map(
            ({name: propertyName, typeAnnotation: nullableTypeAnnotation}) => {
              const _unwrapNullable5 = unwrapNullable(nullableTypeAnnotation),
                _unwrapNullable6 = _slicedToArray(_unwrapNullable5, 1),
                params = _unwrapNullable6[0].params;
              return {
                methodName: propertyName,
                paramCount: params.length,
              };
            },
          ),
        });
      })
      .join('\n');
    const fileName = `${libraryName}JSI-generated.cpp`;
    const replacedTemplate = FileTemplate({
      modules,
      libraryName,
    });
    return new Map([[fileName, replacedTemplate]]);
  },
};
