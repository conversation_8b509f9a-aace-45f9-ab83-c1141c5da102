import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Alert,
  RefreshControl,
  TouchableOpacity
} from 'react-native';
import {
  Searchbar,
  Button,
  Card,
  Text,
  Chip,
  FAB,
  ActivityIndicator,
  IconButton
} from 'react-native-paper';
import { donorService } from '../services';
import { getBloodGroupColor } from '../constants/bloodGroups';
import FilterPanel from '../components/FilterPanel';

const HomeScreen = ({ navigation }) => {
  const [donors, setDonors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({});

  useEffect(() => {
    loadDonors();
  }, [filters]);

  const loadDonors = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const data = await donorService.getDonors(filters);
      setDonors(data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load donors: ' + error.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    loadDonors(true);
  }, [filters]);

  const handleApplyFilters = (newFilters) => {
    setFilters(newFilters);
    setShowFilters(false);
  };

  const handleClearFilters = () => {
    setFilters({});
    setShowFilters(false);
  };

  const calculateDaysSinceLastDonation = (lastDonationDate) => {
    const today = new Date();
    const donationDate = new Date(lastDonationDate);
    const diffTime = Math.abs(today - donationDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDonationText = (days) => {
    if (days === 0) return 'Donated today';
    if (days === 1) return 'Donated yesterday';
    if (days < 30) return `Donated ${days} days ago`;
    if (days < 365) {
      const months = Math.floor(days / 30);
      return `Donated ${months} month${months > 1 ? 's' : ''} ago`;
    }
    const years = Math.floor(days / 365);
    return `Donated ${years} year${years > 1 ? 's' : ''} ago`;
  };

  const filteredDonors = donors.filter(donor => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      donor.firstName.toLowerCase().includes(query) ||
      donor.lastName.toLowerCase().includes(query) ||
      donor.bloodGroup.toLowerCase().includes(query) ||
      donor.currentLocation.toLowerCase().includes(query)
    );
  });

  const renderDonorCard = ({ item: donor }) => {
    const daysSinceLastDonation = calculateDaysSinceLastDonation(donor.lastDonationDate);
    const bloodGroupColor = getBloodGroupColor(donor.bloodGroup);

    return (
      <Card style={styles.donorCard} onPress={() => navigation.navigate('DonorDetails', { donor })}>
        <Card.Content>
          <View style={styles.donorHeader}>
            <View style={styles.donorInfo}>
              <Text style={styles.donorName}>
                {donor.firstName} {donor.lastName.charAt(0)}.
              </Text>
              <Text style={styles.donorLocation}>{donor.currentLocation}</Text>
            </View>
            <View style={styles.bloodGroupContainer}>
              <Chip
                style={[styles.bloodGroupChip, { backgroundColor: bloodGroupColor }]}
                textStyle={styles.bloodGroupText}
              >
                {donor.bloodGroup}
              </Chip>
            </View>
          </View>

          <View style={styles.donorDetails}>
            <Text style={styles.donationText}>
              {formatDonationText(daysSinceLastDonation)}
            </Text>

            <View style={styles.availabilityContainer}>
              <Chip
                style={[
                  styles.availabilityChip,
                  { backgroundColor: donor.isAvailable ? '#27ae60' : '#e74c3c' }
                ]}
                textStyle={styles.availabilityText}
              >
                {donor.isAvailable ? 'Available' : 'Unavailable'}
              </Chip>
            </View>
          </View>

          <View style={styles.contactContainer}>
            <Button
              mode="outlined"
              onPress={() => Alert.alert('Contact', `Phone: ${donor.phoneNumber}`)}
              style={styles.contactButton}
              compact
            >
              Show Contact
            </Button>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateText}>No donors found</Text>
      <Text style={styles.emptyStateSubtext}>
        {searchQuery ? 'Try adjusting your search' : 'Be the first to register!'}
      </Text>
      <Button
        mode="contained"
        onPress={() => navigation.navigate('DonorRegistration')}
        style={styles.emptyStateButton}
      >
        Register as Donor
      </Button>
    </View>
  );

  if (loading && donors.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#e74c3c" />
        <Text style={styles.loadingText}>Loading donors...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <Searchbar
        placeholder="Search donors by name, blood group, or location"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
        iconColor="#e74c3c"
      />

      {/* Filter Button */}
      <View style={styles.filterContainer}>
        <Button
          mode="outlined"
          onPress={() => setShowFilters(!showFilters)}
          style={styles.filterButton}
          icon="filter"
        >
          Filters
        </Button>

        <Button
          mode="outlined"
          onPress={() => navigation.navigate('AdminPanel')}
          style={styles.adminButton}
          icon="cog"
        >
          Admin
        </Button>
      </View>

      {/* Donor List */}
      <FlatList
        data={filteredDonors}
        renderItem={renderDonorCard}
        keyExtractor={(item) => item.id.toString()}
        style={styles.donorList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#e74c3c']}
          />
        }
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={filteredDonors.length === 0 ? styles.emptyListContainer : null}
      />

      {/* Filter Panel */}
      <FilterPanel
        filters={filters}
        onFiltersChange={setFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        visible={showFilters}
      />

      {/* Floating Action Button */}
      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => navigation.navigate('DonorRegistration')}
        color="#fff"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  searchBar: {
    margin: 16,
    elevation: 2,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
    justifyContent: 'space-between',
  },
  filterButton: {
    flex: 1,
    marginRight: 8,
  },
  adminButton: {
    flex: 1,
    marginLeft: 8,
  },
  donorList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  donorCard: {
    marginBottom: 12,
    elevation: 2,
  },
  donorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  donorInfo: {
    flex: 1,
  },
  donorName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  donorLocation: {
    fontSize: 14,
    color: '#666',
  },
  bloodGroupContainer: {
    marginLeft: 12,
  },
  bloodGroupChip: {
    elevation: 1,
  },
  bloodGroupText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  donorDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  donationText: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  availabilityContainer: {
    marginLeft: 12,
  },
  availabilityChip: {
    elevation: 1,
  },
  availabilityText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 10,
  },
  contactContainer: {
    alignItems: 'flex-end',
  },
  contactButton: {
    borderColor: '#e74c3c',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginBottom: 20,
    textAlign: 'center',
  },
  emptyStateButton: {
    backgroundColor: '#e74c3c',
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#e74c3c',
  },
});

export default HomeScreen;
