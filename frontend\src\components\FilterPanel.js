import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { 
  Text, 
  Button, 
  Chip, 
  Switch, 
  Card,
  Title,
  Divider 
} from 'react-native-paper';
import { Picker } from '@react-native-picker/picker';
import { BLOOD_GROUPS } from '../constants/bloodGroups';
import { locationService } from '../services';

const FilterPanel = ({ 
  filters, 
  onFiltersChange, 
  onApplyFilters, 
  onClearFilters,
  visible 
}) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [divisions, setDivisions] = useState([]);
  const [zilas, setZilas] = useState([]);
  const [upazilas, setUpazilas] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      loadDivisions();
    }
  }, [visible]);

  useEffect(() => {
    if (localFilters.divisionId) {
      loadZilas(localFilters.divisionId);
    } else {
      setZilas([]);
      setUpazilas([]);
    }
  }, [localFilters.divisionId]);

  useEffect(() => {
    if (localFilters.zilaId) {
      loadUpazilas(localFilters.zilaId);
    } else {
      setUpazilas([]);
    }
  }, [localFilters.zilaId]);

  const loadDivisions = async () => {
    setLoading(true);
    try {
      const data = await locationService.getDivisions();
      setDivisions(data);
    } catch (error) {
      console.error('Failed to load divisions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadZilas = async (divisionId) => {
    try {
      const data = await locationService.getZilas(divisionId);
      setZilas(data);
    } catch (error) {
      console.error('Failed to load zilas:', error);
    }
  };

  const loadUpazilas = async (zilaId) => {
    try {
      const data = await locationService.getUpazilas(zilaId);
      setUpazilas(data);
    } catch (error) {
      console.error('Failed to load upazilas:', error);
    }
  };

  const updateFilter = (key, value) => {
    const newFilters = { ...localFilters, [key]: value };
    
    // Reset dependent filters when parent changes
    if (key === 'divisionId') {
      newFilters.zilaId = null;
      newFilters.upazilaId = null;
    } else if (key === 'zilaId') {
      newFilters.upazilaId = null;
    }
    
    setLocalFilters(newFilters);
  };

  const toggleBloodGroup = (bloodGroup) => {
    const currentGroups = localFilters.bloodGroups || [];
    const newGroups = currentGroups.includes(bloodGroup)
      ? currentGroups.filter(bg => bg !== bloodGroup)
      : [...currentGroups, bloodGroup];
    
    updateFilter('bloodGroups', newGroups);
  };

  const handleApplyFilters = () => {
    // Convert blood groups array to single value for API compatibility
    const apiFilters = { ...localFilters };
    if (apiFilters.bloodGroups && apiFilters.bloodGroups.length === 1) {
      apiFilters.bloodGroup = apiFilters.bloodGroups[0];
      delete apiFilters.bloodGroups;
    } else if (apiFilters.bloodGroups && apiFilters.bloodGroups.length > 1) {
      // For multiple blood groups, we'll need to make multiple API calls
      // For now, just use the first one
      apiFilters.bloodGroup = apiFilters.bloodGroups[0];
      delete apiFilters.bloodGroups;
    }
    
    onApplyFilters(apiFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters = {};
    setLocalFilters(clearedFilters);
    onClearFilters();
  };

  if (!visible) return null;

  return (
    <Card style={styles.container}>
      <Card.Content>
        <Title style={styles.title}>Filter Donors</Title>
        
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Blood Group Filter */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Blood Group</Text>
            <View style={styles.bloodGroupContainer}>
              {BLOOD_GROUPS.map((bloodGroup) => (
                <Chip
                  key={bloodGroup.value}
                  selected={(localFilters.bloodGroups || []).includes(bloodGroup.value)}
                  onPress={() => toggleBloodGroup(bloodGroup.value)}
                  style={[
                    styles.bloodGroupChip,
                    (localFilters.bloodGroups || []).includes(bloodGroup.value) && {
                      backgroundColor: bloodGroup.color
                    }
                  ]}
                  textStyle={[
                    styles.bloodGroupChipText,
                    (localFilters.bloodGroups || []).includes(bloodGroup.value) && {
                      color: '#fff'
                    }
                  ]}
                >
                  {bloodGroup.label}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Location Filter */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location</Text>
            
            {/* Division */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Division</Text>
              <View style={styles.pickerWrapper}>
                <Picker
                  selectedValue={localFilters.divisionId}
                  onValueChange={(value) => updateFilter('divisionId', value)}
                  enabled={!loading}
                  style={styles.picker}
                >
                  <Picker.Item label="All Divisions" value={null} />
                  {divisions.map((division) => (
                    <Picker.Item 
                      key={division.id} 
                      label={division.name} 
                      value={division.id} 
                    />
                  ))}
                </Picker>
              </View>
            </View>

            {/* Zila */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Zila/District</Text>
              <View style={styles.pickerWrapper}>
                <Picker
                  selectedValue={localFilters.zilaId}
                  onValueChange={(value) => updateFilter('zilaId', value)}
                  enabled={localFilters.divisionId && zilas.length > 0}
                  style={styles.picker}
                >
                  <Picker.Item label="All Zilas" value={null} />
                  {zilas.map((zila) => (
                    <Picker.Item 
                      key={zila.id} 
                      label={zila.name} 
                      value={zila.id} 
                    />
                  ))}
                </Picker>
              </View>
            </View>

            {/* Upazila */}
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Upazila</Text>
              <View style={styles.pickerWrapper}>
                <Picker
                  selectedValue={localFilters.upazilaId}
                  onValueChange={(value) => updateFilter('upazilaId', value)}
                  enabled={localFilters.zilaId && upazilas.length > 0}
                  style={styles.picker}
                >
                  <Picker.Item label="All Upazilas" value={null} />
                  {upazilas.map((upazila) => (
                    <Picker.Item 
                      key={upazila.id} 
                      label={upazila.name} 
                      value={upazila.id} 
                    />
                  ))}
                </Picker>
              </View>
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Availability Filter */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Availability</Text>
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Available Only</Text>
              <Switch
                value={localFilters.available === 'true'}
                onValueChange={(value) => updateFilter('available', value ? 'true' : undefined)}
                color="#e74c3c"
              />
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Last Donation Filter */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Last Donation</Text>
            <View style={styles.donationFilterContainer}>
              {[
                { label: 'Within 3 months', value: 90 },
                { label: 'Within 6 months', value: 180 },
                { label: 'Within 1 year', value: 365 },
              ].map((option) => (
                <Chip
                  key={option.value}
                  selected={localFilters.lastDonationWithin === option.value}
                  onPress={() => updateFilter(
                    'lastDonationWithin', 
                    localFilters.lastDonationWithin === option.value ? undefined : option.value
                  )}
                  style={styles.donationChip}
                >
                  {option.label}
                </Chip>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={handleClearFilters}
            style={styles.clearButton}
          >
            Clear All
          </Button>
          <Button
            mode="contained"
            onPress={handleApplyFilters}
            style={styles.applyButton}
          >
            Apply Filters
          </Button>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    maxHeight: '80%',
    elevation: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e74c3c',
    textAlign: 'center',
    marginBottom: 16,
  },
  scrollView: {
    maxHeight: 400,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  bloodGroupContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  bloodGroupChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  bloodGroupChipText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  pickerContainer: {
    marginBottom: 12,
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    color: '#666',
  },
  pickerWrapper: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  picker: {
    height: 40,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 14,
    color: '#333',
  },
  donationFilterContainer: {
    flexDirection: 'column',
    gap: 8,
  },
  donationChip: {
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  divider: {
    marginVertical: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  clearButton: {
    flex: 1,
    borderColor: '#e74c3c',
  },
  applyButton: {
    flex: 1,
    backgroundColor: '#e74c3c',
  },
});

export default FilterPanel;
