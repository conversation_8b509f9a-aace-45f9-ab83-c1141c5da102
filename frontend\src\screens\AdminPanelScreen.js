import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  RefreshControl
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Text,
  Divider,
  DataTable,
  ActivityIndicator,
  Chip
} from 'react-native-paper';
import { donorService, locationService } from '../services';
import { BLOOD_GROUPS, getBloodGroupColor } from '../constants/bloodGroups';

const AdminPanelScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState({
    totalDonors: 0,
    availableDonors: 0,
    recentDonors: 0,
    bloodGroupStats: {},
  });
  const [recentDonors, setRecentDonors] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // Load all donors to calculate statistics
      const allDonors = await donorService.getDonors();

      // Calculate statistics
      const stats = calculateStatistics(allDonors);
      setStatistics(stats);

      // Get recent donors (last 10)
      const recent = allDonors
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 10);
      setRecentDonors(recent);

    } catch (error) {
      Alert.alert('Error', 'Failed to load dashboard data: ' + error.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const calculateStatistics = (donors) => {
    const total = donors.length;
    const available = donors.filter(d => d.isAvailable).length;

    // Recent donors (registered in last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recent = donors.filter(d => new Date(d.createdAt) > thirtyDaysAgo).length;

    // Blood group statistics
    const bloodGroupStats = {};
    BLOOD_GROUPS.forEach(bg => {
      bloodGroupStats[bg.value] = donors.filter(d => d.bloodGroup === bg.value).length;
    });

    return {
      totalDonors: total,
      availableDonors: available,
      recentDonors: recent,
      bloodGroupStats,
    };
  };

  const onRefresh = () => {
    loadDashboardData(true);
  };

  const handleViewAllDonors = () => {
    navigation.navigate('Home');
  };

  const handleAddDonor = () => {
    navigation.navigate('DonorRegistration');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#e74c3c" />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={['#e74c3c']}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <Title style={styles.headerTitle}>Admin Dashboard</Title>
          <Paragraph style={styles.headerSubtitle}>
            Manage donors and view statistics
          </Paragraph>
        </Card.Content>
      </Card>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Text style={styles.statNumber}>{statistics.totalDonors}</Text>
            <Text style={styles.statLabel}>Total Donors</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Text style={styles.statNumber}>{statistics.availableDonors}</Text>
            <Text style={styles.statLabel}>Available</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Text style={styles.statNumber}>{statistics.recentDonors}</Text>
            <Text style={styles.statLabel}>New (30 days)</Text>
          </Card.Content>
        </Card>
      </View>

      {/* Blood Group Statistics */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Blood Group Distribution</Title>
          <Divider style={styles.divider} />

          <View style={styles.bloodGroupGrid}>
            {BLOOD_GROUPS.map((bloodGroup) => (
              <View key={bloodGroup.value} style={styles.bloodGroupItem}>
                <Chip
                  style={[
                    styles.bloodGroupChip,
                    { backgroundColor: bloodGroup.color }
                  ]}
                  textStyle={styles.bloodGroupChipText}
                >
                  {bloodGroup.value}
                </Chip>
                <Text style={styles.bloodGroupCount}>
                  {statistics.bloodGroupStats[bloodGroup.value] || 0}
                </Text>
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>

      {/* Recent Donors */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Recent Donors</Title>
          <Divider style={styles.divider} />

          {recentDonors.length > 0 ? (
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Name</DataTable.Title>
                <DataTable.Title>Blood Group</DataTable.Title>
                <DataTable.Title>Location</DataTable.Title>
                <DataTable.Title>Status</DataTable.Title>
              </DataTable.Header>

              {recentDonors.map((donor) => (
                <DataTable.Row
                  key={donor.id}
                  onPress={() => navigation.navigate('DonorDetails', { donor })}
                >
                  <DataTable.Cell>
                    {donor.firstName} {donor.lastName.charAt(0)}.
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Chip
                      style={[
                        styles.tableBloodGroupChip,
                        { backgroundColor: getBloodGroupColor(donor.bloodGroup) }
                      ]}
                      textStyle={styles.tableBloodGroupText}
                    >
                      {donor.bloodGroup}
                    </Chip>
                  </DataTable.Cell>
                  <DataTable.Cell numberOfLines={1}>
                    {donor.currentLocation}
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Chip
                      style={[
                        styles.statusChip,
                        { backgroundColor: donor.isAvailable ? '#27ae60' : '#e74c3c' }
                      ]}
                      textStyle={styles.statusChipText}
                    >
                      {donor.isAvailable ? 'Available' : 'Unavailable'}
                    </Chip>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No donors registered yet</Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Quick Actions</Title>
          <Divider style={styles.divider} />

          <View style={styles.actionButtons}>
            <Button
              mode="contained"
              onPress={handleAddDonor}
              style={styles.actionButton}
              icon="plus"
            >
              Add New Donor
            </Button>

            <Button
              mode="outlined"
              onPress={handleViewAllDonors}
              style={styles.actionButton}
              icon="view-list"
            >
              View All Donors
            </Button>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 4,
    backgroundColor: '#e74c3c',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.9,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  card: {
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 8,
  },
  divider: {
    marginBottom: 16,
  },
  bloodGroupGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  bloodGroupItem: {
    width: '23%',
    alignItems: 'center',
    marginBottom: 16,
  },
  bloodGroupChip: {
    elevation: 1,
    marginBottom: 8,
  },
  bloodGroupChipText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  bloodGroupCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  tableBloodGroupChip: {
    elevation: 1,
  },
  tableBloodGroupText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 10,
  },
  statusChip: {
    elevation: 1,
  },
  statusChipText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
});

export default AdminPanelScreen;
