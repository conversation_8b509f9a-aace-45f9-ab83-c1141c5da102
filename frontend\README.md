# LifeDonor Frontend

A React Native Expo application for connecting blood donors with those in need.

## Features

### 🩸 Donor Registration
- Complete registration form with validation
- Blood group selection with color coding
- Location selection (Division → Zila → Upazila)
- Last donation date tracking
- Availability status toggle
- Phone number validation for Bangladesh

### 🔍 Donor Search & Discovery
- Search by name, blood group, or location
- Advanced filtering options
- Real-time availability status
- Days since last donation display
- Paginated donor list with pull-to-refresh

### 📱 Donor Details
- Comprehensive donor information
- Contact information with privacy protection
- Call and SMS integration
- Report functionality for inappropriate listings
- Location details with hierarchy

### ⚙️ Admin Panel
- Dashboard with statistics
- Blood group distribution charts
- Recent donors management
- Quick action buttons
- Donor management interface

### 🎨 UI/UX Features
- Material Design with React Native Paper
- Consistent color coding for blood groups
- Responsive design for different screen sizes
- Loading states and error handling
- Pull-to-refresh functionality
- Floating action buttons

## Tech Stack

- **Framework**: React Native with Expo
- **UI Library**: React Native Paper
- **Navigation**: React Navigation v6
- **Forms**: React Hook Form
- **HTTP Client**: Axios
- **State Management**: React Hooks
- **Date Picker**: React Native Community DateTimePicker
- **Icons**: Expo Vector Icons

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── BloodGroupPicker.js
│   ├── LocationPicker.js
│   ├── FilterPanel.js
│   └── index.js
├── screens/            # Screen components
│   ├── HomeScreen.js
│   ├── DonorRegistrationScreen.js
│   ├── DonorDetailsScreen.js
│   ├── AdminPanelScreen.js
│   └── index.js
├── services/           # API service layer
│   ├── api.js
│   ├── donorService.js
│   ├── locationService.js
│   └── index.js
├── constants/          # App constants
│   ├── api.js
│   ├── bloodGroups.js
│   └── theme.js
└── utils/              # Utility functions
    ├── helpers.js
    ├── commonStyles.js
    └── index.js
```

## Installation

1. **Prerequisites**
   - Node.js (v16 or higher)
   - npm or yarn
   - Expo CLI (`npm install -g @expo/cli`)

2. **Install Dependencies**
   ```bash
   cd frontend
   npm install
   ```

3. **Start Development Server**
   ```bash
   npm start
   ```

4. **Run on Device/Simulator**
   - Install Expo Go app on your mobile device
   - Scan the QR code from the terminal
   - Or press `a` for Android emulator, `i` for iOS simulator

## Configuration

### API Configuration
Update the API base URL in `src/constants/api.js`:
```javascript
export const API_BASE_URL = 'http://your-backend-url:5000/api';
```

### Theme Customization
Modify colors and styling in `src/constants/theme.js`:
```javascript
export const colors = {
  primary: '#e74c3c',  // Main brand color
  // ... other colors
};
```

## Key Components

### LocationPicker
Hierarchical location selection with dependent dropdowns:
- Division → Zila → Upazila
- Automatic loading of dependent options
- Error handling and loading states

### BloodGroupPicker
Blood group selection with visual indicators:
- Color-coded blood group options
- Consistent styling across the app

### FilterPanel
Advanced filtering interface:
- Multiple blood group selection
- Location-based filtering
- Availability and donation time filters
- Apply/clear functionality

## API Integration

The app integrates with the backend API for:
- Donor registration and management
- Location data (divisions, zilas, upazilas)
- Search and filtering
- Statistics for admin panel

### Error Handling
- Network error handling
- User-friendly error messages
- Retry mechanisms
- Offline state handling

## Blood Group System

The app implements a comprehensive blood group system:
- 8 blood groups (A+, A-, B+, B-, AB+, AB-, O+, O-)
- Color coding for visual identification
- Compatibility checking (future feature)

## Privacy & Security

- Phone number masking in donor lists
- "Show Contact" button for privacy protection
- Report functionality for inappropriate content
- Data validation and sanitization

## Performance Optimizations

- Lazy loading of location data
- Debounced search functionality
- Optimized re-renders with React.memo
- Efficient list rendering with FlatList

## Future Enhancements

- [ ] Push notifications for urgent requests
- [ ] GPS-based location detection
- [ ] Blood compatibility matching
- [ ] Donation history tracking
- [ ] Multi-language support
- [ ] Dark mode theme
- [ ] Offline data caching

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
